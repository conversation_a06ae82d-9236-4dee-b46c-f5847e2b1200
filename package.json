{"name": "stellarium-web", "version": "0.1.0", "private": true, "description": "Stellarium Web is an online planetarium that lets you discover the night sky. It's easy to use and beautiful.", "author": "Stellarium Labs <<EMAIL>>", "scripts": {"build": "NODE_OPTIONS=\"--openssl-legacy-provider\" vue-cli-service build", "lint": "vue-cli-service lint", "dev": "NODE_OPTIONS=\"--openssl-legacy-provider\" vue-cli-service serve", "i18n": "vue-cli-service i18n:report"}, "dependencies": {"@mdi/font": "^3.6.95", "core-js": "^3.4.4", "leaflet": "^1.7.1", "leaflet-control-geocoder": "^1.13.0", "moment": "^2.29.4", "roboto-fontface": "*", "v-click-outside": "^3.1.2", "vue": "^2.6.12", "vue-cookie": "^1.1.4", "vue-fullscreen": "^2.1.5", "vue-i18n": "^8.0.0", "vue-jsonp": "^0.1.8", "vue-router": "^3.0.3", "vue2-leaflet": "^2.6.0", "vuetify": "^2.6.10", "vuex": "^3.0.1"}, "devDependencies": {"@kazupon/vue-i18n-loader": "^0.3.0", "@vue/cli-plugin-babel": "~4.5.15", "@vue/cli-plugin-eslint": "~4.5.15", "@vue/cli-service": "~4.5.15", "@vue/eslint-config-standard": "^5.1.0", "babel-eslint": "^10.0.3", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.1", "eslint-plugin-node": "^11.0.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.1.2", "eslint-plugin-vuetify": "^1.0.0-beta.3", "sass": "^1.26.10", "sass-loader": "^10.0.2", "vue-cli-plugin-i18n": "~2.3.1", "vue-cli-plugin-vuetify": "~2.4.3", "vue-template-compiler": "^2.6.12", "vuetify-loader": "^1.2.2"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "@vue/standard"], "plugins": ["vuetify"], "rules": {"vuetify/no-deprecated-classes": "error", "vue/no-parsing-error": [2, {"x-invalid-end-tag": false}]}, "parserOptions": {"parser": "babel-es<PERSON>"}}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions"], "eslintIgnore": ["/build/", "/dist/", "/*.js", "src/assets/js/*.js"], "workspaces": ["src/plugins/*"]}