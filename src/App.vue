// Stellarium Web - Copyright (c) 2022 - Stellarium Labs SRL
//
// This program is licensed under the terms of the GNU AGPL v3, or
// alternatively under a commercial licence.
//
// The terms of the AGPL v3 license can be found in the main directory of this
// repository.

<template>

<v-app>
  <v-navigation-drawer v-model="nav" app stateless width="300">
    <v-layout column fill-height>
      <v-list dense>
        <template v-for="(item,i) in menuItems">
          <template v-if="$store.state[item.store_show_menu_item] === false"></template>
          <v-subheader v-else-if="item.header" v-text="item.header" class="grey--text text--darken-1" :key="i"/>
          <v-divider class="divider_menu" v-else-if="item.divider" :key="i"/>
          <v-list-item v-else-if="item.switch" @click.stop="toggleStoreValue(item.store_var_name)" :key="i">
            <v-list-item-action>
              <v-switch value :input-value="getStoreValue(item.store_var_name)" label=""></v-switch>
            </v-list-item-action>
            <v-list-item-content>
              <v-list-item-title>{{ item.title }}</v-list-item-title>
            </v-list-item-content>
          </v-list-item>
          <template v-else>
            <v-list-item v-if='item.link' target="_blank" rel="noopener" :href='item.link' :key="i">
              <v-list-item-icon><v-icon>{{ item.icon }}</v-icon></v-list-item-icon>
              <v-list-item-title v-text="item.title"/>
              <v-icon disabled>mdi-open-in-new</v-icon>
            </v-list-item>
            <v-list-item v-else-if='item.action' @click.stop="handleMenuAction(item.action)" :key="i">
              <v-list-item-icon><v-icon>{{ item.icon }}</v-icon></v-list-item-icon>
              <v-list-item-title v-text="item.title"/>
            </v-list-item>
            <v-list-item v-else-if='item.footer===undefined' @click.stop="toggleStoreValue(item.store_var_name)" :key="i">
              <v-list-item-icon><v-icon>{{ item.icon }}</v-icon></v-list-item-icon>
              <v-list-item-title v-text="item.title"/>
            </v-list-item>
          </template>
        </template>
      </v-list>
      <template v-for="(item,i) in menuComponents">
        <component :is="item" :key="i"></component>
      </template>
      <v-spacer></v-spacer>
      <v-list dense>
        <v-divider class="divider_menu"/>
        <template v-for="(item,i) in menuItems">
          <v-list-item v-if='item.footer' @click.stop="toggleStoreValue(item.store_var_name)" :key="i">
            <v-list-item-icon><v-icon>{{ item.icon }}</v-icon></v-list-item-icon>
            <v-list-item-title v-text="item.title"/>
          </v-list-item>
        </template>
      </v-list>
    </v-layout>
  </v-navigation-drawer>

  <v-main>
    <v-container class="fill-height" fluid style="padding: 0">
      <div id="stel" v-bind:class="{ right_panel: $store.state.showSidePanel }">
        <div style="position: relative; width: 100%; height: 100%">
          <component v-bind:is="guiComponent"></component>
          <canvas id="stel-canvas" ref='stelCanvas'></canvas>
        </div>
      </div>
    </v-container>
  </v-main>

</v-app>

</template>

<script>

import _ from 'lodash'
import Gui from '@/components/gui.vue'
import GuiLoader from '@/components/gui-loader.vue'
import swh from '@/assets/sw_helpers.js'
import Moment from 'moment'

export default {
  data (context) {
    return {
      menuItems: [
        { title: this.$t('View Settings'), icon: 'mdi-settings', store_var_name: 'showViewSettingsDialog', store_show_menu_item: 'showViewSettingsMenuItem' },
        { title: this.$t('Planets Tonight'), icon: 'mdi-panorama-fisheye', store_var_name: 'showPlanetsVisibilityDialog', store_show_menu_item: 'showPlanetsVisibilityMenuItem' },
        { title: this.$t('Test Load Image'), icon: 'mdi-image', action: 'testLoadImage' },
        { title: this.$t('Test Circle Layer'), icon: 'mdi-circle-outline', action: 'testCreateCircleLayer' },
        { divider: true }
      ].concat(this.getPluginsMenuItems()).concat([
        { title: this.$t('Data Credits'), footer: true, icon: 'mdi-copyright', store_var_name: 'showDataCreditsDialog' }
      ]),
      menuComponents: [].concat(this.getPluginsMenuComponents()),
      guiComponent: 'GuiLoader',
      startTimeIsSet: false,
      initDone: false,
      dataSourceInitDone: false,
      testImageLayer: undefined,
      testCircleLayer: undefined
    }
  },
  components: { Gui, GuiLoader },
  methods: {
    getPluginsMenuItems: function () {
      let res = []
      for (const i in this.$stellariumWebPlugins()) {
        const plugin = this.$stellariumWebPlugins()[i]
        if (plugin.menuItems) {
          res = res.concat(plugin.menuItems)
        }
      }
      return res
    },
    getPluginsMenuComponents: function () {
      let res = []
      for (const i in this.$stellariumWebPlugins()) {
        const plugin = this.$stellariumWebPlugins()[i]
        if (plugin.menuComponents) {
          res = res.concat(plugin.menuComponents)
        }
      }
      return res
    },
    toggleStoreValue: function (storeVarName) {
      this.$store.commit('toggleBool', storeVarName)
    },
    getStoreValue: function (storeVarName) {
      return _.get(this.$store.state, storeVarName)
    },
    handleMenuAction: function (action) {
      if (action === 'testLoadImage') {
        this.testLoadImage()
      } else if (action === 'testCreateCircleLayer') {
        this.testCreateCircleLayer()
      }
    },
    testLoadImage: function () {
      console.log('Testing image loading with createLayer...')

      if (!this.$stel) {
        console.error('Stellarium engine not ready')
        return
      }

      // Remove existing test layer if any
      if (this.testImageLayer) {
        this.testImageLayer.destroy()
        this.testImageLayer = undefined
        console.log('Removed existing test image layer')
        return
      }

      try {
        // Create a new layer for testing
        this.testImageLayer = this.$stel.createLayer({
          id: 'test-image-layer',
          z: 60,
          visible: true
        })

        console.log('Created test layer:', this.testImageLayer)

        // Try to add different types of objects to test image loading
        const imageUrl = process.env.BASE_URL + 'icons8-arrow.png'
        console.log('Loading image from:', imageUrl)
        this.$stel.createLayer({
          id: 'test-image-layer',
          z: 60,
          visible: true
        })
        this.$stel.createLayer({
          id: 'test-image-layer',
          z: 7,
          visible: true
        })

        // Method 1: Try to add an image object
        try {
          // const imageParams = {
          //   pos: [0, 0, 1, 0], // Zenith position
          //   // frame: this.$stel.FRAME_VIEW,
          //   size: [0.2, 0.2], // Size in radians
          //   texture: imageUrl,
          //   color: [1, 1, 1, 1]
          // }

          // const imageObj = this.testImageLayer.add('image', imageParams)
          // if (imageObj) {
          //   console.log('Successfully added image object:', imageObj)
          //   return
          // }
        } catch (e) {
          console.log('Image type failed:', e.message)
        }

        // // Method 2: Try to add a textured quad
        // try {
        //   const quadParams = {
        //     pos: [0, 0, 1, 0],
        //     frame: this.$stel.FRAME_OBSERVED,
        //     size: [0.2, 0.2],
        //     texture: imageUrl
        //   }

        //   const quadObj = this.testImageLayer.add('quad', quadParams)
        //   if (quadObj) {
        //     console.log('Successfully added quad with texture:', quadObj)
        //     return
        //   }
        // } catch (e) {
        //   console.log('Quad type failed:', e.message)
        // }

        // // Method 3: Try to add a simple circle as fallback
        // try {
        //   const circleParams = {
        //     pos: [0, 0, 1, 0],
        //     frame: this.$stel.FRAME_OBSERVED,
        //     size: [0.1, 0.1],
        //     color: [1, 0, 0, 0.8], // Red color
        //     border_color: [1, 1, 1, 1] // White border
        //   }

        //   const circleObj = this.testImageLayer.add('circle', circleParams)
        //   if (circleObj) {
        //     console.log('Successfully added circle as fallback:', circleObj)
        //     return
        //   }
        // } catch (e) {
        //   console.log('Circle type failed:', e.message)
        // }

        console.log('All object types failed')
      } catch (error) {
        console.error('Error in testLoadImage:', error)
      }
    },
    testCreateCircleLayer: function () {
      console.log('Testing circle layer creation...')

      if (!this.$stel) {
        console.error('Stellarium engine not ready')
        return
      }

      // Remove existing test circle layer if any
      if (this.testCircleLayer) {
        this.testCircleLayer.destroy()
        this.testCircleLayer = undefined
        console.log('Removed existing test circle layer')
        return
      }

      try {
        // Create a new layer for testing circles
        this.testCircleLayer = this.$stel.createLayer({
          id: 'test-circle-layer',
          z: 65,
          visible: true
        })

        console.log('Created test circle layer:', this.testCircleLayer)

        // Add a red circle at zenith
        const circleParams1 = {
          pos: [0, 0, 1, 0], // Zenith position
          frame: this.$stel.FRAME_OBSERVED,
          size: [0.1, 0.1], // Size in radians
          color: [1, 0, 0, 0.8], // Red color with transparency
          border_color: [1, 1, 1, 1] // White border
        }

        const circleObj1 = this.testCircleLayer.add('circle', circleParams1)
        if (circleObj1) {
          console.log('Successfully added red circle at zenith:', circleObj1)
        }

        // Add a blue circle at a different position
        const circleParams2 = {
          pos: [0.5, 0, 0.866, 0], // 30 degrees from zenith
          frame: this.$stel.FRAME_OBSERVED,
          size: [0.05, 0.05], // Smaller size
          color: [0, 0, 1, 0.6], // Blue color with transparency
          border_color: [1, 1, 0, 1] // Yellow border
        }

        const circleObj2 = this.testCircleLayer.add('circle', circleParams2)
        if (circleObj2) {
          console.log('Successfully added blue circle:', circleObj2)
        }

        // Add a green circle at another position
        const circleParams3 = {
          pos: [-0.5, 0, 0.866, 0], // 30 degrees from zenith, opposite side
          frame: this.$stel.FRAME_OBSERVED,
          size: [0.08, 0.08], // Medium size
          color: [0, 1, 0, 0.7], // Green color with transparency
          border_color: [1, 0, 1, 1] // Magenta border
        }

        const circleObj3 = this.testCircleLayer.add('circle', circleParams3)
        if (circleObj3) {
          console.log('Successfully added green circle:', circleObj3)
        }

        console.log('Test circle layer created successfully with 3 circles!')

      } catch (error) {
        console.error('Error in testCreateCircleLayer:', error)
      }

    },
    setStateFromQueryArgs: function () {
      // Check whether the observing panel must be displayed
      this.$store.commit('setValue', { varName: 'showSidePanel', newValue: this.$route.path.startsWith('/p/') })

      // Set the core's state from URL query arguments such
      // as date, location, view direction & fov
      var that = this

      if (!this.initDone) {
        this.$stel.core.time_speed = 1
        let d = new Date()
        if (this.$route.query.date) {
          d = new Moment(this.$route.query.date).toDate()
          this.$stel.core.observer.utc = d.getMJD()
          this.startTimeIsSet = true
        }

        if (this.$route.query.lng && this.$route.query.lat) {
          const pos = { lat: Number(this.$route.query.lat), lng: Number(this.$route.query.lng), alt: this.$route.query.elev ? Number(this.$route.query.elev) : 0, accuracy: 1 }
          swh.geoCodePosition(pos, that).then((loc) => {
            that.$store.commit('setCurrentLocation', loc)
          }, (error) => { console.log(error) })
        }

        this.$stel.core.observer.yaw = this.$route.query.az ? Number(this.$route.query.az) * Math.PI / 180 : 0
        this.$stel.core.observer.pitch = this.$route.query.alt ? Number(this.$route.query.alt) * Math.PI / 180 : 30 * Math.PI / 180
        this.$stel.core.fov = this.$route.query.fov ? Number(this.$route.query.fov) * Math.PI / 180 : 120 * Math.PI / 180

        this.initDone = true
      }

      if (this.$route.path.startsWith('/skysource/')) {
        const name = decodeURIComponent(this.$route.path.substring(11))
        console.log('Will select object: ' + name)
        return swh.lookupSkySourceByName(name).then(ss => {
          if (!ss) {
            return
          }
          let obj = swh.skySource2SweObj(ss)
          if (!obj) {
            obj = this.$stel.createObj(ss.model, ss)
            this.$selectionLayer.add(obj)
          }
          if (!obj) {
            console.warning("Can't find object in SWE: " + ss.names[0])
          }
          swh.setSweObjAsSelection(obj)
        }, err => {
          console.log(err)
          console.log("Couldn't find skysource for name: " + name)
        })
      }
    }
  },
  computed: {
    nav: {
      get: function () {
        return this.$store.state.showNavigationDrawer
      },
      set: function (v) {
        if (this.$store.state.showNavigationDrawer !== v) {
          this.$store.commit('toggleBool', 'showNavigationDrawer')
        }
      }
    },
    storeCurrentLocation: function () {
      return this.$store.state.currentLocation
    }
  },
  watch: {
    storeCurrentLocation: function (loc) {
      const DD2R = Math.PI / 180
      this.$stel.core.observer.latitude = loc.lat * DD2R
      this.$stel.core.observer.longitude = loc.lng * DD2R
      this.$stel.core.observer.elevation = loc.alt

      // At startup, we need to wait for the location to be set before deciding which
      // startup time to set so that it's night time.
      if (!this.startTimeIsSet) {
        this.$stel.core.observer.utc = swh.getTimeAfterSunset(this.$stel)
        this.startTimeIsSet = true
      }
      // Init of time and date is complete
      this.$store.commit('setValue', { varName: 'initComplete', newValue: true })
    },
    $route: function () {
      // react to route changes...
      this.setStateFromQueryArgs()
    }
  },
  mounted: function () {
    var that = this

    for (const i in this.$stellariumWebPlugins()) {
      const plugin = this.$stellariumWebPlugins()[i]
      if (plugin.onAppMounted) {
        plugin.onAppMounted(that)
      }
    }

    import('@/assets/js/stellarium-web-engine.wasm').then(f => {
      // Initialize the StelWebEngine viewer singleton
      // After this call, the StelWebEngine state will always be available in vuex store
      // in the $store.stel object in a reactive way (useful for vue components).
      // To modify the state of the StelWebEngine, it's enough to call/set values directly on the $stel object
      try {
        swh.initStelWebEngine(that.$store, f.default, that.$refs.stelCanvas, function () {
          // Start auto location detection (even if we don't use it)
          swh.getGeolocation().then(p => swh.geoCodePosition(p, that)).then((loc) => {
            that.$store.commit('setAutoDetectedLocation', loc)
          }, (error) => { console.log(error) })

          that.$stel.setFont('regular', process.env.BASE_URL + 'fonts/Roboto-Regular.ttf', 1.38)
          that.$stel.setFont('bold', process.env.BASE_URL + 'fonts/Roboto-Bold.ttf', 1.38)
          that.$stel.core.constellations.show_only_pointed = false

          that.setStateFromQueryArgs()
          that.guiComponent = 'Gui'
          for (const i in that.$stellariumWebPlugins()) {
            const plugin = that.$stellariumWebPlugins()[i]
            if (plugin.onEngineReady) {
              plugin.onEngineReady(that)
            }
          }

          if (!that.dataSourceInitDone) {
            // Set all default data sources
            const core = that.$stel.core
            core.stars.addDataSource({ url: process.env.BASE_URL + 'skydata/stars' })

            // Allow to specify a custom path for sky culture data
            if (that.$route.query.sc) {
              const key = that.$route.query.sc.substring(that.$route.query.sc.lastIndexOf('/') + 1)
              core.skycultures.addDataSource({ url: that.$route.query.sc, key: key })
              core.skycultures.current_id = key
            } else {
              core.skycultures.addDataSource({ url: process.env.BASE_URL + 'skydata/skycultures/western', key: 'western' })
            }

            core.dsos.addDataSource({ url: process.env.BASE_URL + 'skydata/dso' })
            core.landscapes.addDataSource({ url: process.env.BASE_URL + 'skydata/landscapes/guereins', key: 'guereins' })
            core.milkyway.addDataSource({ url: process.env.BASE_URL + 'skydata/surveys/milkyway' })
            core.minor_planets.addDataSource({ url: process.env.BASE_URL + 'skydata/mpcorb.dat', key: 'mpc_asteroids' })
            core.planets.addDataSource({ url: process.env.BASE_URL + 'skydata/surveys/sso/moon', key: 'moon' })
            core.planets.addDataSource({ url: process.env.BASE_URL + 'skydata/surveys/sso/sun', key: 'sun' })
            core.planets.addDataSource({ url: process.env.BASE_URL + 'skydata/surveys/sso/moon', key: 'default' })
            core.comets.addDataSource({ url: process.env.BASE_URL + 'skydata/CometEls.txt', key: 'mpc_comets' })
            core.satellites.addDataSource({ url: process.env.BASE_URL + 'skydata/tle_satellite.jsonl.gz', key: 'jsonl/sat' })
          }
        })
      } catch (e) {
        this.$store.commit('setValue', { varName: 'wasmSupport', newValue: false })
      }
    })
  }
}
</script>

<style>

a {
  color: #82b1ff;
}

a:link {
  text-decoration-line: none;
}

.divider_menu {
  margin-top: 8px;
  margin-bottom: 8px;
}

html {
  overflow-y: visible;
}

html, body, #app {
  overflow-y: visible!important;
  overflow-x: visible;
  position: fixed!important;
  width: 100%;
  height: 100%;
  padding: 0!important;
  font-size: 14px;
}

.fullscreen {
  overflow-y: hidden;
  position: fixed;
  width: 100%;
  height: 100%;
  padding: 0!important;
}

.click-through {
  pointer-events: none;
}

.get-click {
  pointer-events: all;
}

.dialog {
  background: transparent;
}

.menu__content {
  background-color: transparent!important;
}

#stel {height: 100%; width: 100%; position: absolute;}
#stel-canvas {z-index: -10; width: 100%; height: 100%;}

.right_panel {
  padding-right: 400px;
}

.v-btn {
  margin-left: 8px;
  margin-right: 8px;
  margin-top: 6px;
  margin-bottom: 6px;
}

.v-application--wrap {
  min-height: 100%!important;
}

</style>
